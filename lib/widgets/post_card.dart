import 'package:flutter/material.dart';
import '../services/post_service.dart';
import '../services/user_service.dart';

class PostCard extends StatefulWidget {
  final Post post;
  final bool isTablet;
  final VoidCallback? onLike;
  final VoidCallback? onPurchase;
  final VoidCallback? onView;
  final VoidCallback? onTap;

  const PostCard({
    super.key,
    required this.post,
    this.isTablet = false,
    this.onLike,
    this.onPurchase,
    this.onView,
    this.onTap,
  });

  @override
  State<PostCard> createState() => _PostCardState();
}

class _PostCardState extends State<PostCard> {
  final UserService _userService = UserService();
  String? _userProfileImage;
  bool _isLiked = false;

  @override
  void initState() {
    super.initState();
    _loadUserProfileImage();
  }

  Future<void> _loadUserProfileImage() async {
    try {
      final profileImage = await _userService.getUserProfileImage(
        widget.post.authorId,
      );
      if (mounted) {
        setState(() {
          _userProfileImage = profileImage;
        });
      }
    } catch (e) {
      // Handle error silently, will use default avatar
    }
  }

  @override
  Widget build(BuildContext context) {
    final bool isPaidPost = widget.post.price > 0;
    final bool hasAccess = widget.post.isPaid || !isPaidPost;

    return GestureDetector(
      onTap: widget.onTap,
      child: Container(
        margin: EdgeInsets.only(bottom: widget.isTablet ? 16 : 8),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header - Facebook-style user info
            _buildUserHeader(isPaidPost, hasAccess),

            // Post content
            _buildPostContent(hasAccess),

            // Media content (images, videos, links)
            _buildMediaContent(),

            // Interaction section
            _buildInteractionSection(isPaidPost, hasAccess),
          ],
        ),
      ),
    );
  }

  Widget _buildUserHeader(bool isPaidPost, bool hasAccess) {
    return Padding(
      padding: EdgeInsets.all(widget.isTablet ? 16 : 12),
      child: Row(
        children: [
          // User avatar
          CircleAvatar(
            radius: widget.isTablet ? 24 : 20,
            backgroundColor: const Color(0xFF5159FF),
            backgroundImage:
                _userProfileImage != null
                    ? NetworkImage(_userProfileImage!)
                    : null,
            child:
                _userProfileImage == null
                    ? Text(
                      widget.post.author
                          .split(' ')
                          .map((e) => e[0])
                          .take(2)
                          .join(),
                      style: TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                        fontSize: widget.isTablet ? 16 : 14,
                      ),
                    )
                    : null,
          ),
          SizedBox(width: widget.isTablet ? 12 : 10),

          // User info
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Text(
                      widget.post.author,
                      style: TextStyle(
                        fontWeight: FontWeight.w600,
                        fontSize: widget.isTablet ? 16 : 15,
                        color: Colors.black87,
                      ),
                    ),
                    if (isPaidPost) ...[
                      const SizedBox(width: 6),
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 6,
                          vertical: 2,
                        ),
                        decoration: BoxDecoration(
                          color:
                              hasAccess
                                  ? Colors.green
                                  : const Color(0xFF5159FF),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Text(
                          hasAccess ? 'OWNED' : 'PAID',
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 10,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ],
                  ],
                ),
                const SizedBox(height: 2),
                Row(
                  children: [
                    Text(
                      widget.post.timeAgo,
                      style: TextStyle(
                        color: Colors.grey[600],
                        fontSize: widget.isTablet ? 13 : 12,
                      ),
                    ),
                    if (widget.post.category.isNotEmpty) ...[
                      Text(
                        ' • ',
                        style: TextStyle(
                          color: Colors.grey[600],
                          fontSize: widget.isTablet ? 13 : 12,
                        ),
                      ),
                      Text(
                        widget.post.category,
                        style: TextStyle(
                          color: const Color(0xFF5159FF),
                          fontSize: widget.isTablet ? 13 : 12,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ],
                ),
              ],
            ),
          ),

          // More options button
          IconButton(
            onPressed: () => _showPostOptions(context),
            icon: Icon(
              Icons.more_horiz,
              color: Colors.grey[600],
              size: widget.isTablet ? 24 : 20,
            ),
            padding: EdgeInsets.zero,
            constraints: const BoxConstraints(),
          ),
        ],
      ),
    );
  }

  Widget _buildPostContent(bool hasAccess) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Title (if exists)
        if (widget.post.title != null && widget.post.title!.isNotEmpty)
          Padding(
            padding: EdgeInsets.fromLTRB(
              widget.isTablet ? 16 : 12,
              0,
              widget.isTablet ? 16 : 12,
              widget.isTablet ? 12 : 8,
            ),
            child: Text(
              widget.post.title!,
              style: TextStyle(
                fontSize: widget.isTablet ? 20 : 17,
                fontWeight: FontWeight.w600,
                color: Colors.black87,
                height: 1.3,
              ),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ),

        // Content text
        Padding(
          padding: EdgeInsets.fromLTRB(
            widget.isTablet ? 16 : 12,
            0,
            widget.isTablet ? 16 : 12,
            widget.isTablet ? 16 : 12,
          ),
          child: Text(
            hasAccess
                ? widget.post.content
                : '${widget.post.content.substring(0, widget.post.content.length > 120 ? 120 : widget.post.content.length)}${widget.post.content.length > 120 ? '...' : ''}',
            style: TextStyle(
              fontSize: widget.isTablet ? 15 : 14,
              color: hasAccess ? Colors.black87 : Colors.grey[700],
              height: 1.4,
            ),
            maxLines: hasAccess ? null : 4,
            overflow: hasAccess ? null : TextOverflow.ellipsis,
          ),
        ),

        // Tags
        if (widget.post.tags.isNotEmpty)
          Padding(
            padding: EdgeInsets.fromLTRB(
              widget.isTablet ? 16 : 12,
              0,
              widget.isTablet ? 16 : 12,
              widget.isTablet ? 16 : 12,
            ),
            child: Wrap(
              spacing: 6,
              runSpacing: 6,
              children:
                  widget.post.tags.map((tag) {
                    return Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.grey[100],
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        tag,
                        style: TextStyle(
                          color: Colors.grey[700],
                          fontSize: widget.isTablet ? 12 : 10,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    );
                  }).toList(),
            ),
          ),
      ],
    );
  }

  Widget _buildMediaContent() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Images gallery
        if (widget.post.imageUrls.isNotEmpty) _buildImageGallery(),

        // Videos
        if (widget.post.videoUrls.isNotEmpty) ...[
          if (widget.post.imageUrls.isNotEmpty) const SizedBox(height: 8),
          _buildVideoSection(),
        ],

        // URL Link preview
        if (widget.post.linkUrl != null && widget.post.linkUrl!.isNotEmpty) ...[
          if (widget.post.imageUrls.isNotEmpty ||
              widget.post.videoUrls.isNotEmpty)
            const SizedBox(height: 8),
          _buildLinkPreview(),
        ],
      ],
    );
  }

  Widget _buildImageGallery() {
    final images = widget.post.imageUrls;

    if (images.isEmpty) return const SizedBox.shrink();

    return Container(
      margin: EdgeInsets.symmetric(
        horizontal: widget.isTablet ? 16 : 12,
        vertical: 8,
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(12),
        child: _buildImageLayout(images),
      ),
    );
  }

  Widget _buildImageLayout(List<String> images) {
    if (images.length == 1) {
      return AspectRatio(
        aspectRatio: 16 / 9,
        child: Image.network(
          images[0],
          fit: BoxFit.cover,
          errorBuilder:
              (context, error, stackTrace) => Container(
                color: Colors.grey[200],
                child: const Icon(
                  Icons.image_not_supported,
                  color: Colors.grey,
                ),
              ),
        ),
      );
    } else if (images.length == 2) {
      return SizedBox(
        height: 200,
        child: Row(
          children:
              images
                  .map(
                    (url) => Expanded(
                      child: Container(
                        margin: const EdgeInsets.only(right: 2),
                        child: Image.network(
                          url,
                          fit: BoxFit.cover,
                          errorBuilder:
                              (context, error, stackTrace) => Container(
                                color: Colors.grey[200],
                                child: const Icon(
                                  Icons.image_not_supported,
                                  color: Colors.grey,
                                ),
                              ),
                        ),
                      ),
                    ),
                  )
                  .toList(),
        ),
      );
    } else {
      // 3+ images: show first 2 large, then grid for rest
      return SizedBox(
        height: 200,
        child: Row(
          children: [
            Expanded(
              flex: 2,
              child: Image.network(
                images[0],
                fit: BoxFit.cover,
                errorBuilder:
                    (context, error, stackTrace) => Container(
                      color: Colors.grey[200],
                      child: const Icon(
                        Icons.image_not_supported,
                        color: Colors.grey,
                      ),
                    ),
              ),
            ),
            const SizedBox(width: 2),
            Expanded(
              child: Column(
                children: [
                  Expanded(
                    child: Image.network(
                      images[1],
                      fit: BoxFit.cover,
                      errorBuilder:
                          (context, error, stackTrace) => Container(
                            color: Colors.grey[200],
                            child: const Icon(
                              Icons.image_not_supported,
                              color: Colors.grey,
                            ),
                          ),
                    ),
                  ),
                  if (images.length > 2) ...[
                    const SizedBox(height: 2),
                    Expanded(
                      child: Stack(
                        children: [
                          Image.network(
                            images[2],
                            fit: BoxFit.cover,
                            width: double.infinity,
                            errorBuilder:
                                (context, error, stackTrace) => Container(
                                  color: Colors.grey[200],
                                  child: const Icon(
                                    Icons.image_not_supported,
                                    color: Colors.grey,
                                  ),
                                ),
                          ),
                          if (images.length > 3)
                            Container(
                              color: Colors.black.withValues(alpha: 0.6),
                              child: Center(
                                child: Text(
                                  '+${images.length - 3}',
                                  style: const TextStyle(
                                    color: Colors.white,
                                    fontSize: 18,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                            ),
                        ],
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ],
        ),
      );
    }
  }

  Widget _buildVideoSection() {
    return Container(
      margin: EdgeInsets.symmetric(
        horizontal: widget.isTablet ? 16 : 12,
        vertical: 8,
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(12),
        child: AspectRatio(
          aspectRatio: 16 / 9,
          child: Container(
            color: Colors.black,
            child: Stack(
              alignment: Alignment.center,
              children: [
                Container(
                  color: Colors.grey[300],
                  child: const Center(
                    child: Icon(
                      Icons.play_circle_outline,
                      size: 64,
                      color: Colors.white,
                    ),
                  ),
                ),
                Positioned(
                  bottom: 8,
                  right: 8,
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 6,
                      vertical: 2,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.black.withValues(alpha: 0.7),
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Text(
                      '${widget.post.videoUrls.length} video${widget.post.videoUrls.length > 1 ? 's' : ''}',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildLinkPreview() {
    return Container(
      margin: EdgeInsets.symmetric(
        horizontal: widget.isTablet ? 16 : 12,
        vertical: 8,
      ),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey[300]!),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            height: 120,
            decoration: BoxDecoration(
              color: Colors.grey[200],
              borderRadius: const BorderRadius.vertical(
                top: Radius.circular(12),
              ),
            ),
            child: const Center(
              child: Icon(Icons.link, size: 32, color: Colors.grey),
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(12),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  widget.post.linkUrl!,
                  style: TextStyle(
                    fontSize: widget.isTablet ? 14 : 13,
                    fontWeight: FontWeight.w500,
                    color: const Color(0xFF5159FF),
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 4),
                Text(
                  'External Link',
                  style: TextStyle(
                    fontSize: widget.isTablet ? 12 : 11,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInteractionSection(bool isPaidPost, bool hasAccess) {
    return Container(
      padding: EdgeInsets.all(widget.isTablet ? 16 : 12),
      decoration: BoxDecoration(
        border: Border(top: BorderSide(color: Colors.grey[200]!)),
      ),
      child: Row(
        children: [
          // Like button
          _buildActionButton(
            icon: _isLiked ? Icons.favorite : Icons.favorite_border,
            label: '${widget.post.likes}',
            onTap: () {
              setState(() {
                _isLiked = !_isLiked;
              });
              widget.onLike?.call();
            },
            color: _isLiked ? Colors.red : Colors.grey[600]!,
          ),
          SizedBox(width: widget.isTablet ? 24 : 16),

          // Comments button
          _buildActionButton(
            icon: Icons.comment_outlined,
            label: '${widget.post.comments}',
            onTap: () {
              // Handle comment tap
            },
            color: Colors.grey[600]!,
          ),
          SizedBox(width: widget.isTablet ? 24 : 16),

          // Views
          _buildActionButton(
            icon: Icons.visibility_outlined,
            label: '${widget.post.views}',
            onTap: widget.onView,
            color: Colors.grey[600]!,
            isClickable: false,
          ),

          const Spacer(),

          // Share button
          _buildActionButton(
            icon: Icons.share_outlined,
            label: '',
            onTap: () {
              // Handle share
            },
            color: Colors.grey[600]!,
          ),

          // Purchase button for paid posts
          if (isPaidPost && !hasAccess) ...[
            const SizedBox(width: 12),
            ElevatedButton(
              onPressed: widget.onPurchase,
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF5159FF),
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(20),
                ),
                padding: EdgeInsets.symmetric(
                  horizontal: widget.isTablet ? 16 : 12,
                  vertical: widget.isTablet ? 10 : 8,
                ),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Icon(Icons.shopping_cart, size: 16),
                  const SizedBox(width: 6),
                  Text(
                    'Buy ${widget.post.formattedPrice}',
                    style: TextStyle(
                      fontWeight: FontWeight.w600,
                      fontSize: widget.isTablet ? 13 : 12,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required Color color,
    VoidCallback? onTap,
    bool isClickable = true,
  }) {
    final child = Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(icon, size: widget.isTablet ? 20 : 18, color: color),
        if (label.isNotEmpty) ...[
          const SizedBox(width: 6),
          Text(
            label,
            style: TextStyle(
              color: color,
              fontWeight: FontWeight.w600,
              fontSize: widget.isTablet ? 14 : 12,
            ),
          ),
        ],
      ],
    );

    if (isClickable && onTap != null) {
      return GestureDetector(onTap: onTap, child: child);
    }

    return child;
  }

  void _showPostOptions(BuildContext context) {
    showModalBottomSheet(
      context: context,
      builder:
          (context) => Container(
            padding: const EdgeInsets.all(16),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                ListTile(
                  leading: const Icon(Icons.bookmark_border),
                  title: const Text('Save Post'),
                  onTap: () => Navigator.pop(context),
                ),
                ListTile(
                  leading: const Icon(Icons.report_outlined),
                  title: const Text('Report Post'),
                  onTap: () => Navigator.pop(context),
                ),
                ListTile(
                  leading: const Icon(Icons.block),
                  title: const Text('Hide Post'),
                  onTap: () => Navigator.pop(context),
                ),
              ],
            ),
          ),
    );
  }
}
