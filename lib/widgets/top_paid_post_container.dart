import 'package:flutter/material.dart';
import 'package:money_mouthy_two/services/post_service.dart';
import 'package:money_mouthy_two/services/user_service.dart';
import 'package:url_launcher/url_launcher.dart';

class TopPaidPostContainer extends StatefulWidget {
  final String category;
  final Post? topPost;
  final VoidCallback? onTap;

  const TopPaidPostContainer({
    super.key,
    required this.category,
    this.topPost,
    this.onTap,
  });

  @override
  State<TopPaidPostContainer> createState() => _TopPaidPostContainerState();
}

class _TopPaidPostContainerState extends State<TopPaidPostContainer> {
  final UserService _userService = UserService();
  String? _userProfileImage;
  String _userDisplayName = '';
  bool _isLoadingUserData = true;

  @override
  void initState() {
    super.initState();
    _loadUserData();
  }

  @override
  void didUpdateWidget(TopPaidPostContainer oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.topPost?.authorId != widget.topPost?.authorId) {
      _loadUserData();
    }
  }

  Future<void> _loadUserData() async {
    if (widget.topPost == null) {
      setState(() => _isLoadingUserData = false);
      return;
    }

    try {
      final userData = await _userService.getUserData(widget.topPost!.authorId);
      if (mounted) {
        setState(() {
          _userProfileImage =
              userData?['profileImageUrl'] ?? userData?['photoUrl'];
          _userDisplayName =
              userData?['username'] ??
              userData?['name'] ??
              widget.topPost!.author;
          _isLoadingUserData = false;
        });
      }
    } catch (e) {
      debugPrint('Error loading user data: $e');
      if (mounted) {
        setState(() {
          _userDisplayName = widget.topPost!.author;
          _isLoadingUserData = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (widget.topPost == null) {
      return const SizedBox.shrink();
    }

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          colors: [Color(0xFFFFD700), Color(0xFFFFA500)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.orange.withValues(alpha: 0.3),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: widget.onTap,
          borderRadius: BorderRadius.circular(16),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Header with crown icon and category
                Row(
                  children: [
                    const Icon(
                      Icons.emoji_events,
                      color: Colors.white,
                      size: 24,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'TOP PAID POST - ${widget.category}',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                        letterSpacing: 0.5,
                      ),
                    ),
                    const Spacer(),
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.white.withValues(alpha: 0.2),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        widget.topPost!.formattedPrice,
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 12),

                // User info with profile image
                Row(
                  children: [
                    _isLoadingUserData
                        ? Container(
                          width: 40,
                          height: 40,
                          decoration: BoxDecoration(
                            color: Colors.white.withValues(alpha: 0.2),
                            shape: BoxShape.circle,
                          ),
                          child: const Center(
                            child: SizedBox(
                              width: 16,
                              height: 16,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                valueColor: AlwaysStoppedAnimation<Color>(
                                  Colors.white,
                                ),
                              ),
                            ),
                          ),
                        )
                        : CircleAvatar(
                          radius: 20,
                          backgroundColor: Colors.white.withValues(alpha: 0.2),
                          backgroundImage:
                              _userProfileImage != null
                                  ? NetworkImage(_userProfileImage!)
                                  : null,
                          child:
                              _userProfileImage == null
                                  ? Text(
                                    _userDisplayName.isNotEmpty
                                        ? _userDisplayName[0].toUpperCase()
                                        : 'A',
                                    style: const TextStyle(
                                      color: Colors.white,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  )
                                  : null,
                        ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            _isLoadingUserData
                                ? 'Loading...'
                                : _userDisplayName,
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          Text(
                            widget.topPost!.timeAgo,
                            style: TextStyle(
                              color: Colors.white.withValues(alpha: 0.8),
                              fontSize: 12,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 12),

                // Post content with media
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Text content
                      Text(
                        widget.topPost!.content.length > 150
                            ? '${widget.topPost!.content.substring(0, 150)}...'
                            : widget.topPost!.content,
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 14,
                          height: 1.4,
                        ),
                      ),

                      // Media content below text
                      if (_hasMediaContent()) ...[
                        const SizedBox(height: 12),
                        _buildMediaContent(),
                      ],
                    ],
                  ),
                ),
                const SizedBox(height: 12),

                // Stats row
                Row(
                  children: [
                    _buildStatItem(
                      Icons.favorite,
                      widget.topPost!.likes.toString(),
                    ),
                    const SizedBox(width: 16),
                    _buildStatItem(
                      Icons.comment,
                      widget.topPost!.comments.toString(),
                    ),
                    const SizedBox(width: 16),
                    _buildStatItem(
                      Icons.visibility,
                      widget.topPost!.views.toString(),
                    ),
                    const Spacer(),
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.white.withValues(alpha: 0.2),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            Icons.access_time,
                            color: Colors.white.withValues(alpha: 0.8),
                            size: 14,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            '24h reign',
                            style: TextStyle(
                              color: Colors.white.withValues(alpha: 0.8),
                              fontSize: 12,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  bool _hasMediaContent() {
    if (widget.topPost == null) return false;
    return widget.topPost!.imageUrls.isNotEmpty ||
        widget.topPost!.videoUrls.isNotEmpty ||
        (widget.topPost!.linkUrl != null &&
            widget.topPost!.linkUrl!.isNotEmpty);
  }

  Widget _buildMediaContent() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Images
        if (widget.topPost!.imageUrls.isNotEmpty) _buildImageGallery(),

        // Videos
        if (widget.topPost!.videoUrls.isNotEmpty) ...[
          if (widget.topPost!.imageUrls.isNotEmpty) const SizedBox(height: 8),
          _buildVideoSection(),
        ],

        // URL Link
        if (widget.topPost!.linkUrl != null &&
            widget.topPost!.linkUrl!.isNotEmpty) ...[
          if (widget.topPost!.imageUrls.isNotEmpty ||
              widget.topPost!.videoUrls.isNotEmpty)
            const SizedBox(height: 8),
          _buildLinkPreview(),
        ],
      ],
    );
  }

  Widget _buildImageGallery() {
    final images = widget.topPost!.imageUrls;
    if (images.isEmpty) return const SizedBox.shrink();

    return SizedBox(
      height: 120,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: images.length,
        itemBuilder: (context, index) {
          return Container(
            width: 120,
            margin: EdgeInsets.only(right: index < images.length - 1 ? 8 : 0),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8),
              color: Colors.white.withValues(alpha: 0.1),
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: Image.network(
                images[index],
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) {
                  return Container(
                    color: Colors.white.withValues(alpha: 0.1),
                    child: const Icon(
                      Icons.image_not_supported,
                      color: Colors.white,
                      size: 32,
                    ),
                  );
                },
                loadingBuilder: (context, child, loadingProgress) {
                  if (loadingProgress == null) return child;
                  return Container(
                    color: Colors.white.withValues(alpha: 0.1),
                    child: const Center(
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    ),
                  );
                },
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildVideoSection() {
    final videos = widget.topPost!.videoUrls;
    if (videos.isEmpty) return const SizedBox.shrink();

    return SizedBox(
      height: 60,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: videos.length,
        itemBuilder: (context, index) {
          return Container(
            width: 100,
            margin: EdgeInsets.only(right: index < videos.length - 1 ? 8 : 0),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8),
              color: Colors.white.withValues(alpha: 0.1),
            ),
            child: InkWell(
              onTap: () => _launchUrl(videos[index]),
              borderRadius: BorderRadius.circular(8),
              child: const Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.play_circle_filled,
                      color: Colors.white,
                      size: 24,
                    ),
                    SizedBox(height: 4),
                    Text(
                      'Video',
                      style: TextStyle(color: Colors.white, fontSize: 10),
                    ),
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildLinkPreview() {
    final linkUrl = widget.topPost!.linkUrl;
    if (linkUrl == null || linkUrl.isEmpty) return const SizedBox.shrink();

    return InkWell(
      onTap: () => _launchUrl(linkUrl),
      borderRadius: BorderRadius.circular(8),
      child: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          color: Colors.white.withValues(alpha: 0.1),
          border: Border.all(
            color: Colors.white.withValues(alpha: 0.3),
            width: 1,
          ),
        ),
        child: Row(
          children: [
            const Icon(Icons.link, color: Colors.white, size: 16),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                linkUrl.length > 40
                    ? '${linkUrl.substring(0, 40)}...'
                    : linkUrl,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 12,
                  decoration: TextDecoration.underline,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
            const Icon(Icons.open_in_new, color: Colors.white, size: 14),
          ],
        ),
      ),
    );
  }

  Future<void> _launchUrl(String url) async {
    try {
      final uri = Uri.parse(url);
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri, mode: LaunchMode.externalApplication);
      }
    } catch (e) {
      debugPrint('Error launching URL: $e');
    }
  }

  Widget _buildStatItem(IconData icon, String count) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(icon, color: Colors.white.withValues(alpha: 0.8), size: 16),
        const SizedBox(width: 4),
        Text(
          count,
          style: TextStyle(
            color: Colors.white.withValues(alpha: 0.8),
            fontSize: 12,
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }
}
