import 'package:flutter/material.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:money_mouthy_two/screens/create_post.dart';
import 'package:money_mouthy_two/services/wallet_service.dart';
import 'package:money_mouthy_two/services/post_service.dart';
import 'package:money_mouthy_two/services/follow_service.dart';
import 'package:money_mouthy_two/widgets/profile_drawer.dart';
import 'package:money_mouthy_two/screens/profile_screen.dart';
import 'package:money_mouthy_two/widgets/top_paid_post_container.dart';

// Category data model
class CategoryData {
  final String name;
  final Color color;
  final double topPrice;

  const CategoryData({
    required this.name,
    required this.color,
    required this.topPrice,
  });
}

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> with TickerProviderStateMixin {
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  late TabController _tabController;
  int currentCategoryIndex = 0;

  static const List<CategoryData> categories = [
    CategoryData(name: 'News', color: Color(0xFF29CC76), topPrice: 8.75),
    CategoryData(name: 'Politics', color: Color(0xFF4C5DFF), topPrice: 12.50),
    CategoryData(name: 'Sex', color: Color(0xFFFF4081), topPrice: 25.00),
    CategoryData(
      name: 'Entertainment',
      color: Color(0xFFA06A00),
      topPrice: 10.25,
    ),
    CategoryData(name: 'Sports', color: Color(0xFFC43DFF), topPrice: 15.00),
    CategoryData(name: 'Religion', color: Color(0xFF000000), topPrice: 7.50),
  ];

  @override
  void initState() {
    super.initState();
    _initializeServices();
    _tabController = TabController(length: 2, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _initializeServices() async {
    try {
      await WalletService().initialize();
      await PostService().initialize();
      if (mounted) {
        setState(() {
          // Refresh UI after services are ready
        });
      }
    } catch (e) {
      debugPrint('Error initializing services: $e');
    }
  }

  void _previousCategory() {
    setState(() {
      currentCategoryIndex = (currentCategoryIndex - 1) % categories.length;
    });
  }

  void _nextCategory() {
    setState(() {
      currentCategoryIndex = (currentCategoryIndex + 1) % categories.length;
    });
  }

  Future<void> _navigateToCreatePost() async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const CreatePostScreen(),
        settings: RouteSettings(
          arguments: {
            'selectedCategory': categories[currentCategoryIndex].name,
          },
        ),
      ),
    );

    // If post was successfully created, set category to Politics
    if (result != null && result is Map && result['success'] == true) {
      setState(() {
        // Find Politics category index (should be index 1)
        final politicsIndex = categories.indexWhere(
          (cat) => cat.name == 'Politics',
        );
        if (politicsIndex != -1) {
          currentCategoryIndex = politicsIndex;
        }
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final isWideScreen = constraints.maxWidth >= 768;

        return Scaffold(
          key: _scaffoldKey,
          backgroundColor: Colors.white,
          drawer: !isWideScreen ? ProfileDrawer() : null,
          appBar: HomeAppBar(scaffoldKey: _scaffoldKey),
          body: isWideScreen ? _buildWideScreenLayout() : _buildMobileLayout(),
          bottomNavigationBar: HomeBottomNavigationBar(
            currentCategoryIndex: currentCategoryIndex,
            categories: categories,
            onNavigateToCreatePost: _navigateToCreatePost,
          ),
        );
      },
    );
  }

  Widget _buildWideScreenLayout() {
    return Row(
      children: [
        // Permanent sidebar for wide screens
        Container(
          width: 300,
          decoration: BoxDecoration(
            color: Colors.grey.shade50,
            border: Border(right: BorderSide(color: Colors.grey.shade200)),
          ),
          child: ProfileDrawer(),
        ),
        // Main content
        Expanded(
          child: Container(
            constraints: const BoxConstraints(maxWidth: 900),
            child: _buildMainContent(),
          ),
        ),
      ],
    );
  }

  Widget _buildMobileLayout() {
    return _buildMainContent();
  }

  Widget _buildMainContent() {
    return Column(
      children: [
        HomeTabBar(tabController: _tabController),
        Expanded(
          child: TabBarView(
            controller: _tabController,
            children: [
              ExploreTab(
                currentCategoryIndex: currentCategoryIndex,
                categories: categories,
                onPreviousCategory: _previousCategory,
                onNextCategory: _nextCategory,
              ),
              const FollowingTab(),
            ],
          ),
        ),
      ],
    );
  }
}

// Home App Bar Widget
class HomeAppBar extends StatelessWidget implements PreferredSizeWidget {
  final GlobalKey<ScaffoldState> scaffoldKey;

  const HomeAppBar({super.key, required this.scaffoldKey});

  @override
  Widget build(BuildContext context) {
    return AppBar(
      backgroundColor: Colors.white,
      elevation: 0,
      leading: IconButton(
        icon: const Icon(Icons.menu, color: Colors.black),
        onPressed: () {
          scaffoldKey.currentState?.openDrawer();
        },
      ),
      title: Center(
        child: Container(
          width: 40,
          height: 40,
          decoration: const BoxDecoration(
            shape: BoxShape.circle,
            color: Color(0xFF5159FF),
          ),
          child: ClipOval(
            child: Image.asset(
              'assets/images/money_mouth.png',
              fit: BoxFit.cover,
              errorBuilder: (context, error, stackTrace) {
                return Container(
                  decoration: const BoxDecoration(
                    shape: BoxShape.circle,
                    color: Color(0xFF5159FF),
                  ),
                  child: const Icon(
                    Icons.monetization_on,
                    color: Colors.white,
                    size: 24,
                  ),
                );
              },
            ),
          ),
        ),
      ),
      actions: const [
        // Empty actions - no wallet balance shown
      ],
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}

// Home Tab Bar Widget
class HomeTabBar extends StatelessWidget {
  final TabController tabController;

  const HomeTabBar({super.key, required this.tabController});

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        border: Border(bottom: BorderSide(color: Colors.grey.shade200)),
      ),
      child: TabBar(
        controller: tabController,
        labelColor: Colors.black,
        unselectedLabelColor: Colors.grey,
        indicatorColor: const Color(0xFF4C5DFF),
        indicatorWeight: 3,
        tabs: const [Tab(text: 'Explore'), Tab(text: 'Following')],
      ),
    );
  }
}

// Explore Tab Widget
class ExploreTab extends StatelessWidget {
  final int currentCategoryIndex;
  final List<CategoryData> categories;
  final VoidCallback onPreviousCategory;
  final VoidCallback onNextCategory;

  const ExploreTab({
    super.key,
    required this.currentCategoryIndex,
    required this.categories,
    required this.onPreviousCategory,
    required this.onNextCategory,
  });

  @override
  Widget build(BuildContext context) {
    final PostService postService = PostService();
    final currentCategory = categories[currentCategoryIndex].name;
    final topPost = postService.getTopPaidPostForCategory(currentCategory);

    return ListView(
      padding: EdgeInsets.zero,
      children: [
        TopRankedCategorySection(
          currentCategoryIndex: currentCategoryIndex,
          categories: categories,
          onPreviousCategory: onPreviousCategory,
          onNextCategory: onNextCategory,
        ),
        // Top Paid Post Container (24-hour system)
        if (topPost != null)
          TopPaidPostContainer(
            category: currentCategory,
            topPost: topPost,
            onTap: () {
              // Handle tap on top paid post
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('Viewing top paid post in $currentCategory'),
                  duration: const Duration(seconds: 2),
                ),
              );
            },
          ),
        Expanded(child: PostsFeed(category: currentCategory)),
      ],
    );
  }
}

// Top Ranked Category Section Widget
class TopRankedCategorySection extends StatelessWidget {
  final int currentCategoryIndex;
  final List<CategoryData> categories;
  final VoidCallback onPreviousCategory;
  final VoidCallback onNextCategory;

  const TopRankedCategorySection({
    super.key,
    required this.currentCategoryIndex,
    required this.categories,
    required this.onPreviousCategory,
    required this.onNextCategory,
  });

  @override
  Widget build(BuildContext context) {
    final currentCategory = categories[currentCategoryIndex];

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [Colors.white, Colors.grey.shade50],
        ),
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.3),
            spreadRadius: 2,
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
          BoxShadow(
            color: Colors.white.withOpacity(0.8),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(-2, -2),
          ),
        ],
        border: Border.all(color: Colors.grey.shade200, width: 1),
      ),
      child: Row(
        children: [
          // Column 1: Top Ranked text and price
          Expanded(
            flex: 2,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Top Ranked',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.black87,
                  ),
                ),
                const SizedBox(height: 4),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.green.shade100,
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: Colors.green.shade300),
                  ),
                  child: Text(
                    '\$${currentCategory.topPrice.toStringAsFixed(2)}',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                      color: Colors.green.shade700,
                    ),
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(width: 16),
          // Column 2: Category navigation arrows
          // Column 3: Category badge
          CategoryBadge(category: currentCategory),
          const SizedBox(width: 16),
          CategoryNavigationArrows(
            onPreviousCategory: onPreviousCategory,
            onNextCategory: onNextCategory,
          ),
        ],
      ),
    );
  }
}

// Category Navigation Arrows Widget
class CategoryNavigationArrows extends StatelessWidget {
  final VoidCallback onPreviousCategory;
  final VoidCallback onNextCategory;

  const CategoryNavigationArrows({
    super.key,
    required this.onPreviousCategory,
    required this.onNextCategory,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        GestureDetector(
          onTap: onPreviousCategory,
          child: Container(
            padding: const EdgeInsets.all(4),
            decoration: BoxDecoration(
              color: Colors.grey.shade100,
              shape: BoxShape.circle,
              border: Border.all(color: Colors.grey.shade300),
            ),
            child: Icon(
              Icons.keyboard_arrow_up,
              color: Colors.grey.shade600,
              size: 15,
            ),
          ),
        ),
        const SizedBox(height: 8),
        GestureDetector(
          onTap: onNextCategory,
          child: Container(
            padding: const EdgeInsets.all(4),
            decoration: BoxDecoration(
              color: Colors.grey.shade100,
              shape: BoxShape.circle,
              border: Border.all(color: Colors.grey.shade300),
            ),
            child: Icon(
              Icons.keyboard_arrow_down,
              color: Colors.grey.shade600,
              size: 15,
            ),
          ),
        ),
      ],
    );
  }
}

// Category Badge Widget
class CategoryBadge extends StatelessWidget {
  final CategoryData category;

  const CategoryBadge({super.key, required this.category});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: category.color,
        borderRadius: BorderRadius.circular(20),
      ),
      child: Text(
        category.name,
        style: const TextStyle(
          color: Colors.white,
          fontSize: 14,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }
}

// Posts Feed Widget
class PostsFeed extends StatefulWidget {
  final String category;

  const PostsFeed({super.key, required this.category});

  @override
  State<PostsFeed> createState() => _PostsFeedState();
}

class _PostsFeedState extends State<PostsFeed> {
  final PostService _postService = PostService();
  List<Post> _posts = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadPosts();
  }

  @override
  void didUpdateWidget(PostsFeed oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.category != widget.category) {
      _loadPosts();
    }
  }

  Future<void> _loadPosts() async {
    setState(() => _isLoading = true);

    try {
      // Get posts for the current category, sorted by highest paid
      final posts = _postService.getPostsSortedBy(
        'Highest Paid',
        category: widget.category,
      );

      setState(() {
        _posts = posts;
        _isLoading = false;
      });
    } catch (e) {
      print('Error loading posts: $e');
      setState(() => _isLoading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Center(
        child: Padding(
          padding: EdgeInsets.all(32.0),
          child: CircularProgressIndicator(),
        ),
      );
    }

    if (_posts.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.post_add, size: 64, color: Colors.grey[400]),
            const SizedBox(height: 16),
            Text(
              'No posts in ${widget.category}',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w500,
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Be the first to share something!',
              style: TextStyle(fontSize: 14, color: Colors.grey[500]),
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: () {
                Navigator.pushNamed(context, '/create_post');
              },
              icon: const Icon(Icons.add),
              label: const Text('Create First Post'),
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF5159FF),
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(
                  horizontal: 24,
                  vertical: 12,
                ),
              ),
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: _loadPosts,
      child: ListView.builder(
        padding: const EdgeInsets.fromLTRB(12, 8, 12, 80),
        itemCount: _posts.length,
        itemBuilder: (context, index) {
          return _buildPostCard(_posts[index]);
        },
      ),
    );
  }

  Widget _buildPostCard(Post post) {
    final isPaidPost = post.price > 0;

    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[200]!),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.05),
            blurRadius: 4,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with user info
          Row(
            children: [
              CircleAvatar(
                radius: 16,
                backgroundColor: const Color(0xFF5159FF),
                child: Text(
                  post.author.split(' ').map((e) => e[0]).take(2).join(),
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      post.author,
                      style: const TextStyle(
                        fontWeight: FontWeight.w600,
                        fontSize: 14,
                      ),
                    ),
                    Text(
                      _formatTimeAgo(post.createdAt),
                      style: TextStyle(color: Colors.grey[600], fontSize: 12),
                    ),
                  ],
                ),
              ),
              if (isPaidPost)
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: const Color(0xFF5159FF),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    'Paid Post',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 10,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
            ],
          ),
          const SizedBox(height: 12),

          // Post content
          Text(post.content, style: const TextStyle(fontSize: 14, height: 1.4)),

          if (post.imageUrls.isNotEmpty) ...[
            const SizedBox(height: 12),
            // Display images
            if (post.imageUrls.length == 1)
              ClipRRect(
                borderRadius: BorderRadius.circular(8),
                child: Image.network(
                  post.imageUrls.first,
                  width: double.infinity,
                  height: 200,
                  fit: BoxFit.cover,
                  errorBuilder: (context, error, stackTrace) {
                    return Container(
                      height: 200,
                      color: Colors.grey[200],
                      child: const Icon(Icons.broken_image),
                    );
                  },
                ),
              )
            else
              SizedBox(
                height: 120,
                child: ListView.builder(
                  scrollDirection: Axis.horizontal,
                  itemCount: post.imageUrls.length,
                  itemBuilder: (context, index) {
                    return Container(
                      margin: const EdgeInsets.only(right: 8),
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(8),
                        child: Image.network(
                          post.imageUrls[index],
                          width: 120,
                          height: 120,
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) {
                            return Container(
                              width: 120,
                              height: 120,
                              color: Colors.grey[200],
                              child: const Icon(Icons.broken_image),
                            );
                          },
                        ),
                      ),
                    );
                  },
                ),
              ),
          ],

          const SizedBox(height: 12),

          // Post footer with price and actions
          Row(
            children: [
              if (isPaidPost)
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.green[50],
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.green[200]!),
                  ),
                  child: Text(
                    '\$${post.price.toStringAsFixed(2)}',
                    style: TextStyle(
                      color: Colors.green[700],
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              const Spacer(),
              Row(
                children: [
                  Icon(
                    Icons.favorite_border,
                    size: 16,
                    color: Colors.grey[600],
                  ),
                  const SizedBox(width: 4),
                  Text(
                    '${post.likes}',
                    style: TextStyle(color: Colors.grey[600], fontSize: 12),
                  ),
                  const SizedBox(width: 16),
                  Icon(
                    Icons.chat_bubble_outline,
                    size: 16,
                    color: Colors.grey[600],
                  ),
                  const SizedBox(width: 4),
                  Text(
                    '${post.comments}',
                    style: TextStyle(color: Colors.grey[600], fontSize: 12),
                  ),
                  const SizedBox(width: 16),
                  Icon(Icons.visibility, size: 16, color: Colors.grey[600]),
                  const SizedBox(width: 4),
                  Text(
                    '${post.views}',
                    style: TextStyle(color: Colors.grey[600], fontSize: 12),
                  ),
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }

  String _formatTimeAgo(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inDays > 0) {
      return '${difference.inDays}d';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m';
    } else {
      return 'now';
    }
  }
}

// Following Tab Widget
class FollowingTab extends StatefulWidget {
  const FollowingTab({super.key});

  @override
  State<FollowingTab> createState() => _FollowingTabState();
}

class _FollowingTabState extends State<FollowingTab> {
  final FollowService _followService = FollowService();
  final PostService _postService = PostService();
  List<Post> _followingPosts = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadFollowingPosts();
  }

  Future<void> _loadFollowingPosts() async {
    setState(() => _isLoading = true);

    try {
      // Get list of users current user is following
      final followingUserIds = await _followService.getFollowingStream().first;

      if (followingUserIds.isEmpty) {
        setState(() {
          _followingPosts = [];
          _isLoading = false;
        });
        return;
      }

      // Get all posts and filter by following users
      final allPosts = _postService.getAllPosts();
      final followingPosts =
          allPosts
              .where((post) => followingUserIds.contains(post.authorId))
              .toList();

      // Sort by creation date (newest first)
      followingPosts.sort((a, b) => b.createdAt.compareTo(a.createdAt));

      setState(() {
        _followingPosts = followingPosts;
        _isLoading = false;
      });
    } catch (e) {
      print('Error loading following posts: $e');
      setState(() => _isLoading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_followingPosts.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.people_outline, size: 64, color: Colors.grey[400]),
            const SizedBox(height: 16),
            Text(
              'No posts from followed users',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w500,
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Follow other users to see their posts here',
              style: TextStyle(fontSize: 14, color: Colors.grey[500]),
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: () {
                Navigator.pushNamed(context, '/connect');
              },
              icon: const Icon(Icons.person_add),
              label: const Text('Find People to Follow'),
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF5159FF),
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(
                  horizontal: 24,
                  vertical: 12,
                ),
              ),
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: _loadFollowingPosts,
      child: ListView.builder(
        padding: const EdgeInsets.fromLTRB(12, 8, 12, 80),
        itemCount: _followingPosts.length,
        itemBuilder: (context, index) {
          return _buildPostCard(_followingPosts[index]);
        },
      ),
    );
  }

  Widget _buildPostCard(Post post) {
    final isPaidPost = post.price > 0;

    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[200]!),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.05),
            blurRadius: 4,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with user info
          Row(
            children: [
              CircleAvatar(
                radius: 16,
                backgroundColor: const Color(0xFF5159FF),
                child: Text(
                  post.author.split(' ').map((e) => e[0]).take(2).join(),
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      post.author,
                      style: const TextStyle(
                        fontWeight: FontWeight.w600,
                        fontSize: 14,
                      ),
                    ),
                    Text(
                      _formatTimeAgo(post.createdAt),
                      style: TextStyle(color: Colors.grey[600], fontSize: 12),
                    ),
                  ],
                ),
              ),
              if (isPaidPost)
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: const Color(0xFF5159FF),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    'Paid Post',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 10,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
            ],
          ),
          const SizedBox(height: 12),

          // Post content
          Text(post.content, style: const TextStyle(fontSize: 14, height: 1.4)),

          if (post.imageUrls.isNotEmpty) ...[
            const SizedBox(height: 12),
            // Display images
            if (post.imageUrls.length == 1)
              ClipRRect(
                borderRadius: BorderRadius.circular(8),
                child: Image.network(
                  post.imageUrls.first,
                  width: double.infinity,
                  height: 200,
                  fit: BoxFit.cover,
                  errorBuilder: (context, error, stackTrace) {
                    return Container(
                      height: 200,
                      color: Colors.grey[200],
                      child: const Icon(Icons.broken_image),
                    );
                  },
                ),
              )
            else
              SizedBox(
                height: 120,
                child: ListView.builder(
                  scrollDirection: Axis.horizontal,
                  itemCount: post.imageUrls.length,
                  itemBuilder: (context, index) {
                    return Container(
                      margin: const EdgeInsets.only(right: 8),
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(8),
                        child: Image.network(
                          post.imageUrls[index],
                          width: 120,
                          height: 120,
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) {
                            return Container(
                              width: 120,
                              height: 120,
                              color: Colors.grey[200],
                              child: const Icon(Icons.broken_image),
                            );
                          },
                        ),
                      ),
                    );
                  },
                ),
              ),
          ],

          const SizedBox(height: 12),

          // Post footer with price and actions
          Row(
            children: [
              if (isPaidPost)
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.green[50],
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.green[200]!),
                  ),
                  child: Text(
                    '\$${post.price.toStringAsFixed(2)}',
                    style: TextStyle(
                      color: Colors.green[700],
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              const Spacer(),
              Row(
                children: [
                  Icon(
                    Icons.favorite_border,
                    size: 16,
                    color: Colors.grey[600],
                  ),
                  const SizedBox(width: 4),
                  Text(
                    '${post.likes}',
                    style: TextStyle(color: Colors.grey[600], fontSize: 12),
                  ),
                  const SizedBox(width: 16),
                  Icon(
                    Icons.chat_bubble_outline,
                    size: 16,
                    color: Colors.grey[600],
                  ),
                  const SizedBox(width: 4),
                  Text(
                    '${post.comments}',
                    style: TextStyle(color: Colors.grey[600], fontSize: 12),
                  ),
                  const SizedBox(width: 16),
                  Icon(Icons.visibility, size: 16, color: Colors.grey[600]),
                  const SizedBox(width: 4),
                  Text(
                    '${post.views}',
                    style: TextStyle(color: Colors.grey[600], fontSize: 12),
                  ),
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }

  String _formatTimeAgo(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inDays > 0) {
      return '${difference.inDays}d';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m';
    } else {
      return 'now';
    }
  }
}

// Home Bottom Navigation Bar Widget
class HomeBottomNavigationBar extends StatelessWidget {
  final int currentCategoryIndex;
  final List<CategoryData> categories;
  final VoidCallback onNavigateToCreatePost;

  const HomeBottomNavigationBar({
    super.key,
    required this.currentCategoryIndex,
    required this.categories,
    required this.onNavigateToCreatePost,
  });

  @override
  Widget build(BuildContext context) {
    return BottomNavigationBar(
      type: BottomNavigationBarType.fixed,
      selectedItemColor: const Color(0xFF4C5DFF),
      unselectedItemColor: Colors.grey,
      currentIndex: 0,
      items: const [
        BottomNavigationBarItem(icon: Icon(Icons.home), label: ''),
        BottomNavigationBarItem(
          icon: Icon(Icons.chat_bubble_outline),
          label: '',
        ),
        BottomNavigationBarItem(icon: Icon(Icons.add_box_outlined), label: ''),
        BottomNavigationBarItem(icon: Icon(Icons.search), label: ''),
        BottomNavigationBarItem(icon: Icon(Icons.person_outline), label: ''),
      ],
      onTap: (index) => _handleBottomNavTap(context, index),
    );
  }

  void _handleBottomNavTap(BuildContext context, int index) {
    switch (index) {
      case 1:
        Navigator.pushNamed(context, '/chats');
        break;
      case 2:
        onNavigateToCreatePost();
        break;
      case 3:
        Navigator.pushNamed(context, '/search');
        break;
      case 4:
        final uid = FirebaseAuth.instance.currentUser?.uid;
        if (uid != null) {
          Navigator.push(
            context,
            MaterialPageRoute(builder: (_) => ProfileScreen(userId: uid)),
          );
        }
        break;
    }
  }
}
