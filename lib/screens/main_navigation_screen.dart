import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'home_screen.dart';
import 'chat_list_screen.dart';
import 'create_post.dart';
import 'search_screen.dart';
import 'profile_screen.dart';
import '../widgets/profile_drawer.dart';

class MainNavigationScreen extends StatefulWidget {
  const MainNavigationScreen({super.key});

  @override
  State<MainNavigationScreen> createState() => _MainNavigationScreenState();
}

class _MainNavigationScreenState extends State<MainNavigationScreen> {
  int _currentIndex = 0;
  late List<Widget> _screens;
  final GlobalKey<_HomeScreenContentState> _homeScreenKey =
      GlobalKey<_HomeScreenContentState>();

  // Double back to exit functionality
  DateTime? _lastBackPressed;

  @override
  void initState() {
    super.initState();
    final currentUid = FirebaseAuth.instance.currentUser?.uid ?? '';
    _screens = [
      HomeScreenContent(key: _homeScreenKey), // Home content without bottom nav
      const ChatListScreen(),
      const CreatePostScreen(),
      const SearchScreen(),
      ProfileScreen(userId: currentUid),
    ];
  }

  void _onTabTapped(int index) {
    setState(() {
      _currentIndex = index;
    });
  }

  Future<bool> _onWillPop() async {
    final now = DateTime.now();
    const backPressDuration = Duration(seconds: 2);

    if (_lastBackPressed == null ||
        now.difference(_lastBackPressed!) > backPressDuration) {
      _lastBackPressed = now;

      // Show snackbar with exit message
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Press back again to exit'),
          duration: Duration(seconds: 2),
          behavior: SnackBarBehavior.floating,
        ),
      );
      return false; // Don't exit
    }

    return true; // Exit the app
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (didPop, result) async {
        if (didPop) return;
        final shouldExit = await _onWillPop();
        if (shouldExit && mounted) {
          // Exit the app
          SystemNavigator.pop();
        }
      },
      child: Scaffold(
        body: IndexedStack(index: _currentIndex, children: _screens),
        bottomNavigationBar: BottomNavigationBar(
          type: BottomNavigationBarType.fixed,
          selectedItemColor: const Color(0xFF4C5DFF),
          unselectedItemColor: Colors.grey,
          currentIndex: _currentIndex,
          onTap: _onTabTapped,
          items: const [
            BottomNavigationBarItem(icon: Icon(Icons.home), label: ''),
            BottomNavigationBarItem(
              icon: Icon(Icons.chat_bubble_outline),
              label: '',
            ),
            BottomNavigationBarItem(
              icon: Icon(Icons.add_box_outlined),
              label: '',
            ),
            BottomNavigationBarItem(icon: Icon(Icons.search), label: ''),
            BottomNavigationBarItem(
              icon: Icon(Icons.person_outline),
              label: '',
            ),
          ],
        ),
      ),
    );
  }
}

// Extract the home screen content without the bottom navigation
class HomeScreenContent extends StatefulWidget {
  const HomeScreenContent({super.key});

  @override
  State<HomeScreenContent> createState() => _HomeScreenContentState();
}

class _HomeScreenContentState extends State<HomeScreenContent>
    with TickerProviderStateMixin {
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  late TabController _tabController;
  int currentCategoryIndex = 1; // Default to Politics (index 1)

  // Category data
  static const List<CategoryData> categories = [
    CategoryData(name: 'News', color: Color(0xFF2196F3), topPrice: 25.00),
    CategoryData(name: 'Politics', color: Color(0xFF4CAF50), topPrice: 32.50),
    CategoryData(name: 'Sports', color: Color(0xFFFF9800), topPrice: 18.75),
    CategoryData(
      name: 'Entertainment',
      color: Color(0xFFE91E63),
      topPrice: 22.00,
    ),
    CategoryData(name: 'Sex', color: Color(0xFF9C27B0), topPrice: 15.50),
    CategoryData(name: 'Religion', color: Color(0xFF795548), topPrice: 28.25),
  ];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  void _previousCategory() {
    setState(() {
      currentCategoryIndex =
          currentCategoryIndex > 0
              ? currentCategoryIndex - 1
              : categories.length - 1;
    });
  }

  void _nextCategory() {
    setState(() {
      currentCategoryIndex = (currentCategoryIndex + 1) % categories.length;
    });
  }

  void resetToPolitics() {
    setState(() {
      final politicsIndex = categories.indexWhere(
        (cat) => cat.name == 'Politics',
      );
      if (politicsIndex != -1) {
        currentCategoryIndex = politicsIndex;
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final isWideScreen = constraints.maxWidth >= 768;

        return Scaffold(
          key: _scaffoldKey,
          backgroundColor: Colors.white,
          drawer: !isWideScreen ? ProfileDrawer() : null,
          appBar: HomeAppBar(scaffoldKey: _scaffoldKey),
          body: isWideScreen ? _buildWideScreenLayout() : _buildMobileLayout(),
        );
      },
    );
  }

  Widget _buildWideScreenLayout() {
    return Row(
      children: [
        // Left sidebar with profile
        Container(
          width: 300,
          decoration: BoxDecoration(
            border: Border(right: BorderSide(color: Colors.grey.shade200)),
          ),
          child: ProfileDrawer(),
        ),
        // Main content area
        Expanded(child: _buildMainContent()),
      ],
    );
  }

  Widget _buildMobileLayout() {
    return _buildMainContent();
  }

  Widget _buildMainContent() {
    return Column(
      children: [
        HomeTabBar(tabController: _tabController),
        Expanded(
          child: TabBarView(
            controller: _tabController,
            children: [
              ExploreTab(
                currentCategoryIndex: currentCategoryIndex,
                categories: categories,
                onPreviousCategory: _previousCategory,
                onNextCategory: _nextCategory,
              ),
              const FollowingTab(),
            ],
          ),
        ),
      ],
    );
  }
}
