import 'package:flutter/material.dart';
import '../services/post_service.dart';
import '../widgets/post_card.dart';
import '../screens/media_player_screen.dart';

class PostDetailScreen extends StatefulWidget {
  final Post post;

  const PostDetailScreen({
    super.key,
    required this.post,
  });

  @override
  State<PostDetailScreen> createState() => _PostDetailScreenState();
}

class _PostDetailScreenState extends State<PostDetailScreen> {
  final PostService _postService = PostService();
  final TextEditingController _commentController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  
  late Post _currentPost;
  List<Comment> _comments = [];
  bool _isLoadingComments = true;
  bool _isPostingComment = false;

  @override
  void initState() {
    super.initState();
    _currentPost = widget.post;
    _loadComments();
    _incrementViewCount();
  }

  @override
  void dispose() {
    _commentController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  void _loadComments() {
    setState(() {
      _isLoadingComments = true;
    });

    // Simulate loading comments (replace with actual implementation)
    Future.delayed(const Duration(milliseconds: 800), () {
      if (mounted) {
        setState(() {
          _comments = _generateMockComments();
          _isLoadingComments = false;
        });
      }
    });
  }

  List<Comment> _generateMockComments() {
    return [
      Comment(
        id: '1',
        authorName: 'John Doe',
        content: 'Great post! Really insightful perspective on this topic.',
        timestamp: DateTime.now().subtract(const Duration(hours: 2)),
        likes: 5,
      ),
      Comment(
        id: '2',
        authorName: 'Jane Smith',
        content: 'I completely agree with your points. Thanks for sharing!',
        timestamp: DateTime.now().subtract(const Duration(hours: 4)),
        likes: 3,
      ),
      Comment(
        id: '3',
        authorName: 'Mike Johnson',
        content: 'Interesting take. Would love to see more content like this.',
        timestamp: DateTime.now().subtract(const Duration(hours: 6)),
        likes: 8,
      ),
    ];
  }

  void _incrementViewCount() {
    _postService.viewPost(_currentPost.id);
  }

  void _handleLike() {
    _postService.likePost(_currentPost.id);
    setState(() {
      _currentPost = _currentPost.copyWith(
        likes: _currentPost.likes + 1,
      );
    });
  }

  void _handleShare() {
    // Implement share functionality
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Share functionality coming soon!'),
        duration: Duration(seconds: 2),
      ),
    );
  }

  void _postComment() async {
    if (_commentController.text.trim().isEmpty) return;

    setState(() {
      _isPostingComment = true;
    });

    // Simulate posting comment
    await Future.delayed(const Duration(milliseconds: 500));

    final newComment = Comment(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      authorName: 'You', // Replace with actual user name
      content: _commentController.text.trim(),
      timestamp: DateTime.now(),
      likes: 0,
    );

    setState(() {
      _comments.insert(0, newComment);
      _commentController.clear();
      _isPostingComment = false;
    });

    // Scroll to top to show new comment
    _scrollController.animateTo(
      0,
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeOut,
    );
  }

  void _openMediaPlayer() {
    if (_currentPost.videoUrls.isNotEmpty) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => MediaPlayerScreen(
            videoUrls: _currentPost.videoUrls,
            initialIndex: 0,
            postTitle: _currentPost.title ?? _currentPost.content.substring(0, 30),
            authorName: _currentPost.author,
          ),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          onPressed: () => Navigator.pop(context),
          icon: const Icon(Icons.arrow_back, color: Colors.black),
        ),
        title: const Text(
          'Post',
          style: TextStyle(
            color: Colors.black,
            fontSize: 18,
            fontWeight: FontWeight.w600,
          ),
        ),
        actions: [
          IconButton(
            onPressed: _handleShare,
            icon: const Icon(Icons.share_outlined, color: Colors.black),
          ),
          PopupMenuButton<String>(
            icon: const Icon(Icons.more_vert, color: Colors.black),
            onSelected: (value) {
              switch (value) {
                case 'save':
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('Post saved!')),
                  );
                  break;
                case 'report':
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('Post reported!')),
                  );
                  break;
              }
            },
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'save',
                child: Row(
                  children: [
                    Icon(Icons.bookmark_outline),
                    SizedBox(width: 12),
                    Text('Save Post'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'report',
                child: Row(
                  children: [
                    Icon(Icons.report_outlined),
                    SizedBox(width: 12),
                    Text('Report Post'),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
      body: Column(
        children: [
          // Post Content
          Expanded(
            child: SingleChildScrollView(
              controller: _scrollController,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Enhanced Post Card
                  Container(
                    color: Colors.white,
                    child: PostCard(
                      post: _currentPost,
                      onLike: _handleLike,
                      onPurchase: () {
                        // Handle purchase
                      },
                      onView: () {
                        // Already viewing
                      },
                      onTap: () {
                        // Already in detail view
                      },
                      isDetailView: true,
                    ),
                  ),
                  
                  const SizedBox(height: 8),
                  
                  // Comments Section
                  Container(
                    color: Colors.white,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Padding(
                          padding: const EdgeInsets.all(16),
                          child: Row(
                            children: [
                              const Icon(Icons.comment_outlined, size: 20),
                              const SizedBox(width: 8),
                              Text(
                                'Comments (${_comments.length})',
                                style: const TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ],
                          ),
                        ),
                        
                        if (_isLoadingComments)
                          const Padding(
                            padding: EdgeInsets.all(32),
                            child: Center(child: CircularProgressIndicator()),
                          )
                        else if (_comments.isEmpty)
                          const Padding(
                            padding: EdgeInsets.all(32),
                            child: Center(
                              child: Text(
                                'No comments yet. Be the first to comment!',
                                style: TextStyle(
                                  color: Colors.grey,
                                  fontSize: 14,
                                ),
                              ),
                            ),
                          )
                        else
                          ListView.separated(
                            shrinkWrap: true,
                            physics: const NeverScrollableScrollPhysics(),
                            itemCount: _comments.length,
                            separatorBuilder: (context, index) => Divider(
                              height: 1,
                              color: Colors.grey[200],
                            ),
                            itemBuilder: (context, index) {
                              return _buildCommentItem(_comments[index]);
                            },
                          ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
          
          // Comment Input
          _buildCommentInput(),
        ],
      ),
    );
  }

  Widget _buildCommentItem(Comment comment) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // User Avatar
          CircleAvatar(
            radius: 16,
            backgroundColor: Colors.grey[300],
            child: Text(
              comment.authorName[0].toUpperCase(),
              style: const TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
          
          const SizedBox(width: 12),
          
          // Comment Content
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Text(
                      comment.authorName,
                      style: const TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(width: 8),
                    Text(
                      _formatTimestamp(comment.timestamp),
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ),
                
                const SizedBox(height: 4),
                
                Text(
                  comment.content,
                  style: const TextStyle(fontSize: 14),
                ),
                
                const SizedBox(height: 8),
                
                Row(
                  children: [
                    GestureDetector(
                      onTap: () {
                        // Handle comment like
                      },
                      child: Row(
                        children: [
                          Icon(
                            Icons.favorite_outline,
                            size: 16,
                            color: Colors.grey[600],
                          ),
                          const SizedBox(width: 4),
                          Text(
                            '${comment.likes}',
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.grey[600],
                            ),
                          ),
                        ],
                      ),
                    ),
                    
                    const SizedBox(width: 16),
                    
                    GestureDetector(
                      onTap: () {
                        // Handle reply
                      },
                      child: Text(
                        'Reply',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey[600],
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCommentInput() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(
          top: BorderSide(color: Colors.grey[200]!),
        ),
      ),
      padding: EdgeInsets.only(
        left: 16,
        right: 16,
        top: 12,
        bottom: MediaQuery.of(context).viewInsets.bottom + 12,
      ),
      child: Row(
        children: [
          // User Avatar
          CircleAvatar(
            radius: 16,
            backgroundColor: Colors.grey[300],
            child: const Icon(Icons.person, size: 16),
          ),
          
          const SizedBox(width: 12),
          
          // Comment Input Field
          Expanded(
            child: TextField(
              controller: _commentController,
              decoration: InputDecoration(
                hintText: 'Add a comment...',
                hintStyle: TextStyle(color: Colors.grey[500]),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(20),
                  borderSide: BorderSide(color: Colors.grey[300]!),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(20),
                  borderSide: const BorderSide(color: Color(0xFF5159FF)),
                ),
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 8,
                ),
              ),
              maxLines: null,
              textCapitalization: TextCapitalization.sentences,
            ),
          ),
          
          const SizedBox(width: 8),
          
          // Send Button
          GestureDetector(
            onTap: _isPostingComment ? null : _postComment,
            child: Container(
              width: 36,
              height: 36,
              decoration: BoxDecoration(
                color: _commentController.text.trim().isNotEmpty
                    ? const Color(0xFF5159FF)
                    : Colors.grey[300],
                shape: BoxShape.circle,
              ),
              child: _isPostingComment
                  ? const SizedBox(
                      width: 16,
                      height: 16,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        color: Colors.white,
                      ),
                    )
                  : const Icon(
                      Icons.send,
                      color: Colors.white,
                      size: 18,
                    ),
            ),
          ),
        ],
      ),
    );
  }

  String _formatTimestamp(DateTime timestamp) {
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inMinutes < 1) {
      return 'now';
    } else if (difference.inHours < 1) {
      return '${difference.inMinutes}m';
    } else if (difference.inDays < 1) {
      return '${difference.inHours}h';
    } else {
      return '${difference.inDays}d';
    }
  }
}

// Comment Model
class Comment {
  final String id;
  final String authorName;
  final String content;
  final DateTime timestamp;
  final int likes;

  Comment({
    required this.id,
    required this.authorName,
    required this.content,
    required this.timestamp,
    required this.likes,
  });
}

// Extension for Post copyWith
extension PostCopyWith on Post {
  Post copyWith({
    String? id,
    String? author,
    String? authorId,
    String? title,
    String? content,
    double? price,
    String? category,
    List<String>? tags,
    DateTime? createdAt,
    int? likes,
    int? comments,
    int? views,
    bool? isPaid,
    bool? isPublic,
    bool? allowComments,
    String? imageUrl,
    List<String>? imageUrls,
    List<String>? videoUrls,
    String? linkUrl,
  }) {
    return Post(
      id: id ?? this.id,
      author: author ?? this.author,
      authorId: authorId ?? this.authorId,
      title: title ?? this.title,
      content: content ?? this.content,
      price: price ?? this.price,
      category: category ?? this.category,
      tags: tags ?? this.tags,
      createdAt: createdAt ?? this.createdAt,
      likes: likes ?? this.likes,
      comments: comments ?? this.comments,
      views: views ?? this.views,
      isPaid: isPaid ?? this.isPaid,
      isPublic: isPublic ?? this.isPublic,
      allowComments: allowComments ?? this.allowComments,
      imageUrl: imageUrl ?? this.imageUrl,
      imageUrls: imageUrls ?? this.imageUrls,
      videoUrls: videoUrls ?? this.videoUrls,
      linkUrl: linkUrl ?? this.linkUrl,
    );
  }
}
