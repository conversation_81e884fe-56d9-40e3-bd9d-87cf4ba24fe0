rules_version = '2';

service firebase.storage {
  match /b/{bucket}/o {
    // Allow anyone to read files
    match /{allPaths=**} {
      allow read;
    }

    // Allow authenticated users to write to user_uploads
    match /user_uploads/{allPaths=**} {
      allow write: if request.auth != null;
    }

    // Allow authenticated users to write to post_images
    match /post_images/{allPaths=**} {
      allow write: if request.auth != null;
    }

    // Allow authenticated users to write to profile_images
    match /profile_images/{allPaths=**} {
      allow write: if request.auth != null;
    }
  }
}
